# Firebase Setup for PG Management App

## Prerequisites
1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable the following services in your Firebase project:
   - Authentication (Email/Password)
   - Cloud Firestore
   - Cloud Storage
   - Cloud Messaging

## Setup Steps

### 1. Download google-services.json
1. Go to your Firebase project settings
2. Download the `google-services.json` file
3. Place it in the `app/` directory of your Android project

### 2. Enable Authentication
1. Go to Authentication > Sign-in method
2. Enable "Email/Password" provider

### 3. Configure Firestore Database
1. Go to Firestore Database
2. Create database in production mode
3. Set up the following security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Properties - owners can manage their properties, residents can read
    match /properties/{propertyId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (resource.data.ownerId == request.auth.uid || 
         request.auth.token.userType == 'ADMIN');
    }
    
    // Bookings - users can manage their own bookings
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null && 
        (resource.data.tenantId == request.auth.uid || 
         resource.data.ownerId == request.auth.uid ||
         request.auth.token.userType == 'ADMIN');
    }
    
    // Payments - users can manage their own payments
    match /payments/{paymentId} {
      allow read, write: if request.auth != null && 
        (resource.data.tenantId == request.auth.uid || 
         resource.data.ownerId == request.auth.uid ||
         request.auth.token.userType == 'ADMIN');
    }
    
    // Maintenance requests
    match /maintenance_requests/{requestId} {
      allow read, write: if request.auth != null && 
        (resource.data.tenantId == request.auth.uid || 
         resource.data.ownerId == request.auth.uid ||
         request.auth.token.userType == 'ADMIN');
    }
    
    // Reviews - users can read all, write their own
    match /reviews/{reviewId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        resource.data.tenantId == request.auth.uid;
    }
    
    // Notifications - users can read their own
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
  }
}
```

### 4. Configure Cloud Storage
1. Go to Storage
2. Set up the following security rules:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Profile images
    match /profile_images/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Property images
    match /property_images/{propertyId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // Maintenance images
    match /maintenance_images/{requestId}/{allPaths=**} {
      allow read, write: if request.auth != null;
    }
    
    // Documents
    match /documents/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

### 5. Configure Cloud Messaging
1. Go to Cloud Messaging
2. No additional setup required - the app will handle FCM token registration

## Testing
1. Build and run the app
2. Register a new user account
3. Verify that user data is stored in Firestore
4. Test authentication flow

## Production Considerations
1. Update Firestore security rules for production
2. Set up proper backup and monitoring
3. Configure proper indexes for queries
4. Set up Cloud Functions for server-side logic if needed

## Troubleshooting
- Make sure `google-services.json` is in the correct location
- Verify that all Firebase services are enabled
- Check that security rules are properly configured
- Ensure your app's package name matches the one in Firebase project
