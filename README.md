# 🏠 PG Management Android App

A comprehensive Android application for managing PG (Paying Guest) accommodations, built with modern Android development practices and Firebase backend.

## ✨ Features

### 🔐 User Authentication
- Secure login/sign-up for PG owners and residents
- Firebase Authentication integration
- Role-based access control
- Password reset functionality

### 📊 Dashboard
- **Owner Dashboard**: Manage multiple properties, view occupancy, track payments
- **Resident Dashboard**: View rent details, maintenance requests, payment history
- Real-time statistics and analytics
- Recent activities feed

### 🏢 Property Management
- Add, edit, and delete property listings
- Property details: location, rent, amenities, rules
- Image gallery for properties
- Room management and availability tracking

### 🔍 Property Search
- Advanced search filters (location, price, amenities)
- Interactive property listings
- Detailed property views with images
- Reviews and ratings system

### 📅 Booking System
- Request bookings for available properties
- Booking status tracking
- Lease agreement management
- Check-in/check-out process

### 💳 Payment Management
- Secure payment gateway integration
- Payment history and tracking
- Automated payment reminders
- Multiple payment methods support

### 🔧 Maintenance Requests
- Submit maintenance requests with images
- Priority-based request handling
- Status tracking and updates
- Owner notification system

### 🔔 Notifications
- Push notifications via Firebase Cloud Messaging
- Payment reminders and alerts
- Maintenance status updates
- Booking confirmations

### ⭐ Reviews & Ratings
- Property review system
- Rating breakdown by categories
- Verified resident reviews
- Review moderation

## 🛠️ Tech Stack

- **Language**: Kotlin
- **UI Framework**: Material Design 3
- **Architecture**: MVVM with LiveData
- **Navigation**: Navigation Component
- **Backend**: Firebase (Auth, Firestore, Storage, FCM)
- **Image Loading**: Glide
- **Networking**: Retrofit + OkHttp
- **Database**: Room (local caching)
- **Permissions**: Dexter
- **Maps**: Google Maps SDK
- **Animations**: Lottie

## 📱 Project Structure

```
app/
├── src/main/
│   ├── java/com/example/pgmanagement/
│   │   ├── ui/
│   │   │   ├── auth/           # Authentication screens
│   │   │   ├── dashboard/      # Dashboard and overview
│   │   │   ├── properties/     # Property management
│   │   │   ├── bookings/       # Booking system
│   │   │   ├── payments/       # Payment handling
│   │   │   ├── maintenance/    # Maintenance requests
│   │   │   ├── reviews/        # Reviews and ratings
│   │   │   ├── profile/        # User profile
│   │   │   └── main/          # Main activity
│   │   ├── data/
│   │   │   ├── models/        # Data classes
│   │   │   └── repository/    # Data repositories
│   │   ├── services/          # Background services
│   │   ├── utils/             # Utility classes
│   │   └── PGManagementApplication.kt
│   ├── res/
│   │   ├── layout/            # XML layouts
│   │   ├── values/            # Colors, strings, themes
│   │   ├── drawable/          # Vector drawables
│   │   ├── navigation/        # Navigation graphs
│   │   └── menu/             # Menu resources
│   └── AndroidManifest.xml
├── build.gradle.kts
└── google-services.json      # Firebase config (add this)
```

## 🚀 Setup Instructions

### Prerequisites
- Android Studio Hedgehog or later
- Android SDK 24+ (API level 24)
- Kotlin 2.0+
- Firebase project (see Firebase Setup below)

### Installation
1. Clone or download this project
2. Open Android Studio
3. Select "Open an existing project"
4. Navigate to the project directory
5. Wait for Gradle sync to complete
6. Follow Firebase setup instructions (see `README_FIREBASE_SETUP.md`)
7. Build and run the app

### Firebase Setup
**Important**: You need to set up Firebase before running the app.
See `README_FIREBASE_SETUP.md` for detailed instructions.

## 📱 App Screens

### Authentication Flow
- **Splash Screen**: App logo and loading
- **Login**: Email/password authentication
- **Register**: New user registration with role selection
- **Forgot Password**: Password reset via email

### Main Navigation
- **Dashboard**: Overview and quick stats
- **Properties**: Property listings and management
- **Bookings**: Booking requests and management
- **Payments**: Payment history and transactions
- **Profile**: User settings and logout

### Key Features
- **Role-based UI**: Different interfaces for owners vs residents
- **Real-time updates**: Live data from Firebase
- **Offline support**: Local caching with Room database
- **Push notifications**: FCM integration
- **Material Design 3**: Modern UI with dynamic theming

## 🎨 UI/UX Design

### Material Design 3
- Modern Material You design language
- Dynamic color theming
- Adaptive layouts for different screen sizes
- Smooth animations and transitions

### Color Scheme
- **Primary**: Blue (#2196F3)
- **Secondary**: Orange (#FF9800)
- **Success**: Green (#4CAF50)
- **Error**: Red (#F44336)
- **Warning**: Yellow (#FF9800)

### Typography
- **Headlines**: Sans-serif Medium
- **Body**: Sans-serif Regular
- **Captions**: Sans-serif Light

## 🏗️ Architecture

### MVVM Pattern
- **Model**: Data classes and repositories
- **View**: Activities and Fragments
- **ViewModel**: Business logic and state management

### Key Components
- **Navigation Component**: Single-activity architecture
- **LiveData/StateFlow**: Reactive data binding
- **Repository Pattern**: Data abstraction layer
- **Dependency Injection**: Manual DI (can be upgraded to Hilt)

## 🔧 Development

### Building
```bash
./gradlew assembleDebug    # Debug build
./gradlew assembleRelease  # Release build
```

### Testing
```bash
./gradlew test            # Unit tests
./gradlew connectedAndroidTest  # Instrumented tests
```

## 📋 TODO / Future Enhancements

- [ ] Complete property management features
- [ ] Implement payment gateway integration
- [ ] Add Google Maps integration
- [ ] Implement chat system between owners and residents
- [ ] Add analytics and reporting
- [ ] Implement offline-first architecture
- [ ] Add unit and integration tests
- [ ] Implement CI/CD pipeline
- [ ] Add accessibility features
- [ ] Implement deep linking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact the development team

---

**Built with ❤️ for PG Management** 🏠
