package com.example.pgmanagement.ui.properties

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.example.pgmanagement.R
import com.example.pgmanagement.databinding.FragmentPropertiesBinding
import com.example.pgmanagement.data.models.UserType
import com.example.pgmanagement.utils.Extensions.hide
import com.example.pgmanagement.utils.Extensions.show
import com.example.pgmanagement.utils.PreferenceManager

/**
 * Fragment for displaying properties list
 */
class PropertiesFragment : Fragment() {

    private var _binding: FragmentPropertiesBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var preferenceManager: PreferenceManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPropertiesBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        preferenceManager = PreferenceManager.getInstance(requireContext())
        
        setupUI()
    }

    private fun setupUI() {
        val userType = preferenceManager.userType
        
        // Show different UI based on user type
        when (userType) {
            UserType.PG_OWNER -> setupOwnerView()
            UserType.RESIDENT -> setupResidentView()
            UserType.ADMIN -> setupAdminView()
        }
        
        // Setup click listeners
        with(binding) {
            fabAddProperty.setOnClickListener {
                findNavController().navigate(R.id.action_properties_to_add_property)
            }
            
            btnSearch.setOnClickListener {
                findNavController().navigate(R.id.action_properties_to_search)
            }
        }
    }

    private fun setupOwnerView() {
        with(binding) {
            // Show owner-specific UI
            fabAddProperty.show()
            tvTitle.text = "My Properties"
            tvSubtitle.text = "Manage your PG properties"
        }
    }

    private fun setupResidentView() {
        with(binding) {
            // Hide owner-specific UI
            fabAddProperty.hide()
            tvTitle.text = "Find Properties"
            tvSubtitle.text = "Discover PGs near you"
        }
    }

    private fun setupAdminView() {
        with(binding) {
            // Show all UI for admin
            fabAddProperty.show()
            tvTitle.text = "All Properties"
            tvSubtitle.text = "Manage all properties in the system"
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
