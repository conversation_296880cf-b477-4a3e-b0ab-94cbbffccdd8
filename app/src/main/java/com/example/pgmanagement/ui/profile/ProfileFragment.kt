package com.example.pgmanagement.ui.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.example.pgmanagement.databinding.FragmentProfileBinding
import com.example.pgmanagement.ui.main.MainActivity
import com.example.pgmanagement.utils.PreferenceManager

/**
 * Fragment for user profile and settings
 */
class ProfileFragment : Fragment() {

    private var _binding: FragmentProfileBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var preferenceManager: PreferenceManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProfileBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        preferenceManager = PreferenceManager.getInstance(requireContext())
        
        setupUI()
    }

    private fun setupUI() {
        // Setup logout button
        binding.btnLogout.setOnClickListener {
            (activity as MainActivity).logout()
        }
        
        // Display user info
        binding.tvUserType.text = preferenceManager.userType.name.replace("_", " ")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
