package com.example.pgmanagement.ui.auth

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.example.pgmanagement.R
import com.example.pgmanagement.databinding.FragmentForgotPasswordBinding
import com.example.pgmanagement.utils.Extensions.hideKeyboard
import com.example.pgmanagement.utils.Extensions.isValidEmail
import com.example.pgmanagement.utils.Extensions.showToast
import kotlinx.coroutines.launch

/**
 * Fragment for password reset
 */
class ForgotPasswordFragment : Fragment() {

    private var _binding: FragmentForgotPasswordBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: AuthViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentForgotPasswordBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupUI()
        observeViewModel()
    }

    private fun setupUI() {
        with(binding) {
            // Reset password button click
            btnResetPassword.setOnClickListener {
                hideKeyboard()
                validateAndResetPassword()
            }
            
            // Back to login link click
            tvBackToLogin.setOnClickListener {
                findNavController().navigate(R.id.action_forgotPasswordFragment_to_loginFragment)
            }
        }
    }

    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.authState.collect { state ->
                when (state) {
                    is AuthState.Loading -> {
                        binding.progressBar.visibility = View.VISIBLE
                        binding.btnResetPassword.isEnabled = false
                    }
                    is AuthState.PasswordResetSent -> {
                        binding.progressBar.visibility = View.GONE
                        binding.btnResetPassword.isEnabled = true
                        showToast("Password reset email sent! Check your inbox.")
                        findNavController().navigate(R.id.action_forgotPasswordFragment_to_loginFragment)
                    }
                    is AuthState.Error -> {
                        binding.progressBar.visibility = View.GONE
                        binding.btnResetPassword.isEnabled = true
                        showToast(state.message)
                    }
                    is AuthState.Idle -> {
                        binding.progressBar.visibility = View.GONE
                        binding.btnResetPassword.isEnabled = true
                    }
                    else -> {
                        binding.progressBar.visibility = View.GONE
                        binding.btnResetPassword.isEnabled = true
                    }
                }
            }
        }
    }

    private fun validateAndResetPassword() {
        val email = binding.etEmail.text.toString().trim()

        // Reset errors
        binding.tilEmail.error = null

        var isValid = true

        // Validate email
        if (email.isEmpty()) {
            binding.tilEmail.error = getString(R.string.field_required)
            isValid = false
        } else if (!email.isValidEmail()) {
            binding.tilEmail.error = getString(R.string.invalid_email)
            isValid = false
        }

        if (isValid) {
            viewModel.resetPassword(email)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
