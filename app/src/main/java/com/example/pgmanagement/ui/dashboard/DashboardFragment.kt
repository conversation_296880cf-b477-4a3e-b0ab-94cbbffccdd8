package com.example.pgmanagement.ui.dashboard

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.pgmanagement.R
import com.example.pgmanagement.databinding.FragmentDashboardBinding
import com.example.pgmanagement.data.models.UserType
import com.example.pgmanagement.ui.dashboard.adapters.RecentActivitiesAdapter
import com.example.pgmanagement.utils.Extensions.hide
import com.example.pgmanagement.utils.Extensions.show
import com.example.pgmanagement.utils.Extensions.showToast
import com.example.pgmanagement.utils.PreferenceManager
import kotlinx.coroutines.launch

/**
 * Dashboard fragment showing overview and quick actions
 */
class DashboardFragment : Fragment() {

    private var _binding: FragmentDashboardBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: DashboardViewModel by viewModels()
    private lateinit var preferenceManager: PreferenceManager
    private lateinit var recentActivitiesAdapter: RecentActivitiesAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDashboardBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        preferenceManager = PreferenceManager.getInstance(requireContext())
        
        setupUI()
        setupRecyclerView()
        observeViewModel()
        loadDashboardData()
    }

    private fun setupUI() {
        val userType = preferenceManager.userType
        
        // Show different UI based on user type
        when (userType) {
            UserType.PG_OWNER -> setupOwnerDashboard()
            UserType.RESIDENT -> setupResidentDashboard()
            UserType.ADMIN -> setupAdminDashboard()
        }
        
        // Setup click listeners
        with(binding) {
            cardProperties.setOnClickListener {
                findNavController().navigate(R.id.action_dashboard_to_properties)
            }
            
            cardBookings.setOnClickListener {
                findNavController().navigate(R.id.action_dashboard_to_bookings)
            }
            
            cardPayments.setOnClickListener {
                findNavController().navigate(R.id.action_dashboard_to_payments)
            }
            
            cardMaintenance.setOnClickListener {
                findNavController().navigate(R.id.action_dashboard_to_maintenance)
            }
            
            fabAddProperty.setOnClickListener {
                findNavController().navigate(R.id.action_dashboard_to_add_property)
            }
        }
    }

    private fun setupOwnerDashboard() {
        with(binding) {
            // Show owner-specific cards
            cardProperties.show()
            cardTenants.show()
            cardRevenue.show()
            fabAddProperty.show()
            
            // Hide resident-specific cards
            cardRentDue.hide()
            cardMyBookings.hide()
        }
    }

    private fun setupResidentDashboard() {
        with(binding) {
            // Show resident-specific cards
            cardRentDue.show()
            cardMyBookings.show()
            cardPayments.show()
            cardMaintenance.show()
            
            // Hide owner-specific cards
            cardProperties.hide()
            cardTenants.hide()
            cardRevenue.hide()
            fabAddProperty.hide()
        }
    }

    private fun setupAdminDashboard() {
        with(binding) {
            // Show all cards for admin
            cardProperties.show()
            cardTenants.show()
            cardRevenue.show()
            cardRentDue.show()
            cardMyBookings.show()
            cardPayments.show()
            cardMaintenance.show()
            fabAddProperty.show()
        }
    }

    private fun setupRecyclerView() {
        recentActivitiesAdapter = RecentActivitiesAdapter { activity ->
            // Handle activity item click
            when (activity.type) {
                "booking" -> {
                    // Navigate to booking details
                }
                "payment" -> {
                    // Navigate to payment details
                }
                "maintenance" -> {
                    // Navigate to maintenance details
                }
            }
        }
        
        binding.rvRecentActivities.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = recentActivitiesAdapter
        }
    }

    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.dashboardState.collect { state ->
                when (state) {
                    is DashboardState.Loading -> {
                        binding.progressBar.show()
                    }
                    is DashboardState.Success -> {
                        binding.progressBar.hide()
                        updateDashboardData(state.data)
                    }
                    is DashboardState.Error -> {
                        binding.progressBar.hide()
                        showToast(state.message)
                    }
                    is DashboardState.Idle -> {
                        binding.progressBar.hide()
                    }
                }
            }
        }
    }

    private fun updateDashboardData(data: DashboardData) {
        with(binding) {
            // Update statistics
            tvTotalProperties.text = data.totalProperties.toString()
            tvOccupiedRooms.text = data.occupiedRooms.toString()
            tvPendingPayments.text = "₹${data.pendingPayments}"
            tvMaintenanceRequests.text = data.maintenanceRequests.toString()
            
            // Update recent activities
            recentActivitiesAdapter.submitList(data.recentActivities)
            
            // Show/hide empty state
            if (data.recentActivities.isEmpty()) {
                tvNoActivities.show()
                rvRecentActivities.hide()
            } else {
                tvNoActivities.hide()
                rvRecentActivities.show()
            }
        }
    }

    private fun loadDashboardData() {
        val userId = preferenceManager.userId
        val userType = preferenceManager.userType
        viewModel.loadDashboardData(userId, userType)
    }

    override fun onResume() {
        super.onResume()
        // Refresh data when returning to dashboard
        loadDashboardData()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
