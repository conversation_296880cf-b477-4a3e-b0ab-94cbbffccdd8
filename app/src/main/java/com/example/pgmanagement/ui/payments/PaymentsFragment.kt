package com.example.pgmanagement.ui.payments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.example.pgmanagement.databinding.FragmentPaymentsBinding

/**
 * Fragment for displaying payments
 */
class PaymentsFragment : Fragment() {

    private var _binding: FragmentPaymentsBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPaymentsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Setup UI
        binding.tvTitle.text = "Payments"
        binding.tvSubtitle.text = "Track your payments and transactions"
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
