package com.example.pgmanagement.ui.auth

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.example.pgmanagement.R
import com.example.pgmanagement.databinding.FragmentRegisterBinding
import com.example.pgmanagement.data.models.AuthUser
import com.example.pgmanagement.data.models.UserType
import com.example.pgmanagement.utils.Extensions.hideKeyboard
import com.example.pgmanagement.utils.Extensions.isValidEmail
import com.example.pgmanagement.utils.Extensions.isValidPhone
import com.example.pgmanagement.utils.Extensions.showToast
import com.example.pgmanagement.utils.PreferenceManager
import kotlinx.coroutines.launch

/**
 * Fragment for user registration
 */
class RegisterFragment : Fragment() {

    private var _binding: FragmentRegisterBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: AuthViewModel by viewModels()
    private lateinit var preferenceManager: PreferenceManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentRegisterBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        preferenceManager = PreferenceManager.getInstance(requireContext())
        
        setupUI()
        observeViewModel()
    }

    private fun setupUI() {
        // Setup user type spinner
        val userTypes = arrayOf("PG Owner", "Resident")
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, userTypes)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerUserType.adapter = adapter

        with(binding) {
            // Register button click
            btnRegister.setOnClickListener {
                hideKeyboard()
                validateAndRegister()
            }
            
            // Login link click
            tvLogin.setOnClickListener {
                findNavController().navigate(R.id.action_registerFragment_to_loginFragment)
            }
        }
    }

    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.authState.collect { state ->
                when (state) {
                    is AuthState.Loading -> {
                        binding.progressBar.visibility = View.VISIBLE
                        binding.btnRegister.isEnabled = false
                    }
                    is AuthState.Success -> {
                        binding.progressBar.visibility = View.GONE
                        binding.btnRegister.isEnabled = true
                        
                        // Save user data and navigate to main
                        preferenceManager.isLoggedIn = true
                        preferenceManager.userId = state.user.id
                        preferenceManager.userType = state.user.userType
                        
                        showToast("Registration successful!")
                        (activity as AuthActivity).navigateToMain()
                    }
                    is AuthState.Error -> {
                        binding.progressBar.visibility = View.GONE
                        binding.btnRegister.isEnabled = true
                        showToast(state.message)
                    }
                    is AuthState.Idle -> {
                        binding.progressBar.visibility = View.GONE
                        binding.btnRegister.isEnabled = true
                    }
                    else -> {
                        binding.progressBar.visibility = View.GONE
                        binding.btnRegister.isEnabled = true
                    }
                }
            }
        }
    }

    private fun validateAndRegister() {
        val fullName = binding.etFullName.text.toString().trim()
        val email = binding.etEmail.text.toString().trim()
        val phoneNumber = binding.etPhoneNumber.text.toString().trim()
        val password = binding.etPassword.text.toString().trim()
        val confirmPassword = binding.etConfirmPassword.text.toString().trim()
        val userTypePosition = binding.spinnerUserType.selectedItemPosition

        // Reset errors
        binding.tilFullName.error = null
        binding.tilEmail.error = null
        binding.tilPhoneNumber.error = null
        binding.tilPassword.error = null
        binding.tilConfirmPassword.error = null

        var isValid = true

        // Validate full name
        if (fullName.isEmpty()) {
            binding.tilFullName.error = getString(R.string.field_required)
            isValid = false
        }

        // Validate email
        if (email.isEmpty()) {
            binding.tilEmail.error = getString(R.string.field_required)
            isValid = false
        } else if (!email.isValidEmail()) {
            binding.tilEmail.error = getString(R.string.invalid_email)
            isValid = false
        }

        // Validate phone number
        if (phoneNumber.isEmpty()) {
            binding.tilPhoneNumber.error = getString(R.string.field_required)
            isValid = false
        } else if (!phoneNumber.isValidPhone()) {
            binding.tilPhoneNumber.error = getString(R.string.invalid_phone)
            isValid = false
        }

        // Validate password
        if (password.isEmpty()) {
            binding.tilPassword.error = getString(R.string.field_required)
            isValid = false
        } else if (password.length < 6) {
            binding.tilPassword.error = getString(R.string.password_too_short)
            isValid = false
        }

        // Validate confirm password
        if (confirmPassword.isEmpty()) {
            binding.tilConfirmPassword.error = getString(R.string.field_required)
            isValid = false
        } else if (password != confirmPassword) {
            binding.tilConfirmPassword.error = getString(R.string.passwords_dont_match)
            isValid = false
        }

        if (isValid) {
            val userType = if (userTypePosition == 0) UserType.PG_OWNER else UserType.RESIDENT
            
            val authUser = AuthUser(
                email = email,
                password = password,
                fullName = fullName,
                phoneNumber = phoneNumber,
                userType = userType
            )
            
            viewModel.register(authUser)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
