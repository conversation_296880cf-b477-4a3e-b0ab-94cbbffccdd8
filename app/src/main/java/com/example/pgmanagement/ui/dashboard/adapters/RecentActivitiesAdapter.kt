package com.example.pgmanagement.ui.dashboard.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.pgmanagement.databinding.ItemRecentActivityBinding
import com.example.pgmanagement.ui.dashboard.RecentActivity
import com.example.pgmanagement.utils.Extensions.toTimeAgo

/**
 * Adapter for displaying recent activities in dashboard
 */
class RecentActivitiesAdapter(
    private val onItemClick: (RecentActivity) -> Unit
) : ListAdapter<RecentActivity, RecentActivitiesAdapter.ActivityViewHolder>(ActivityDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ActivityViewHolder {
        val binding = ItemRecentActivityBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ActivityViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ActivityViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ActivityViewHolder(
        private val binding: ItemRecentActivityBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(activity: RecentActivity) {
            with(binding) {
                tvTitle.text = activity.title
                tvDescription.text = activity.description
                tvTimestamp.text = activity.timestamp.toTimeAgo()
                
                // Set icon based on activity type
                when (activity.type) {
                    "booking" -> ivIcon.setImageResource(android.R.drawable.ic_menu_today)
                    "payment" -> ivIcon.setImageResource(android.R.drawable.ic_menu_send)
                    "maintenance" -> ivIcon.setImageResource(android.R.drawable.ic_menu_edit)
                    else -> ivIcon.setImageResource(android.R.drawable.ic_menu_info_details)
                }
                
                root.setOnClickListener {
                    onItemClick(activity)
                }
            }
        }
    }

    private class ActivityDiffCallback : DiffUtil.ItemCallback<RecentActivity>() {
        override fun areItemsTheSame(oldItem: RecentActivity, newItem: RecentActivity): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: RecentActivity, newItem: RecentActivity): Boolean {
            return oldItem == newItem
        }
    }
}
