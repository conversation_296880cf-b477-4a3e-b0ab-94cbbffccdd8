package com.example.pgmanagement.ui.main

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.navigation.ui.setupWithNavController
import com.example.pgmanagement.R
import com.example.pgmanagement.databinding.ActivityMainBinding
import com.example.pgmanagement.ui.auth.AuthActivity
import com.example.pgmanagement.utils.PreferenceManager
import com.google.firebase.auth.FirebaseAuth

/**
 * Main activity that hosts the primary navigation and fragments
 */
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var navController: NavController
    private lateinit var preferenceManager: PreferenceManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        preferenceManager = PreferenceManager.getInstance(this)

        // Check if user is logged in
        if (!preferenceManager.isLoggedIn || FirebaseAuth.getInstance().currentUser == null) {
            navigateToAuth()
            return
        }

        setupNavigation()
        setupBottomNavigation()
    }

    private fun setupNavigation() {
        val navHostFragment = supportFragmentManager
            .findFragmentById(R.id.nav_host_fragment_main) as NavHostFragment
        navController = navHostFragment.navController

        // Setup action bar with navigation controller
        val appBarConfiguration = AppBarConfiguration(
            setOf(
                R.id.dashboardFragment,
                R.id.propertiesFragment,
                R.id.bookingsFragment,
                R.id.paymentsFragment,
                R.id.profileFragment
            )
        )
        setupActionBarWithNavController(navController, appBarConfiguration)
    }

    private fun setupBottomNavigation() {
        binding.bottomNavigation.setupWithNavController(navController)
        
        // Handle navigation item selection
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.dashboardFragment -> {
                    navController.navigate(R.id.dashboardFragment)
                    true
                }
                R.id.propertiesFragment -> {
                    navController.navigate(R.id.propertiesFragment)
                    true
                }
                R.id.bookingsFragment -> {
                    navController.navigate(R.id.bookingsFragment)
                    true
                }
                R.id.paymentsFragment -> {
                    navController.navigate(R.id.paymentsFragment)
                    true
                }
                R.id.profileFragment -> {
                    navController.navigate(R.id.profileFragment)
                    true
                }
                else -> false
            }
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        return navController.navigateUp() || super.onSupportNavigateUp()
    }

    private fun navigateToAuth() {
        val intent = Intent(this, AuthActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    /**
     * Logout user and navigate to authentication
     */
    fun logout() {
        FirebaseAuth.getInstance().signOut()
        preferenceManager.clearUserData()
        navigateToAuth()
    }
}
