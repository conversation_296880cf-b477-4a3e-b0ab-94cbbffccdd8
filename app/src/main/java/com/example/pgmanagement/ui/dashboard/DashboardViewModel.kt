package com.example.pgmanagement.ui.dashboard

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.pgmanagement.data.models.UserType
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

/**
 * ViewModel for Dashboard operations
 */
class DashboardViewModel : ViewModel() {

    private val firestore = FirebaseFirestore.getInstance()
    
    private val _dashboardState = MutableStateFlow<DashboardState>(DashboardState.Idle)
    val dashboardState: StateFlow<DashboardState> = _dashboardState.asStateFlow()

    /**
     * Load dashboard data based on user type
     */
    fun loadDashboardData(userId: String, userType: UserType) {
        viewModelScope.launch {
            try {
                _dashboardState.value = DashboardState.Loading
                
                val data = when (userType) {
                    UserType.PG_OWNER -> loadOwnerDashboardData(userId)
                    UserType.RESIDENT -> loadResidentDashboardData(userId)
                    UserType.ADMIN -> loadAdminDashboardData()
                }
                
                _dashboardState.value = DashboardState.Success(data)
            } catch (e: Exception) {
                _dashboardState.value = DashboardState.Error(e.message ?: "Failed to load dashboard data")
            }
        }
    }

    private suspend fun loadOwnerDashboardData(ownerId: String): DashboardData {
        // Load properties count
        val propertiesSnapshot = firestore.collection("properties")
            .whereEqualTo("ownerId", ownerId)
            .get()
            .await()
        
        val totalProperties = propertiesSnapshot.size()
        
        // Load occupied rooms count
        val bookingsSnapshot = firestore.collection("bookings")
            .whereEqualTo("ownerId", ownerId)
            .whereEqualTo("status", "CHECKED_IN")
            .get()
            .await()
        
        val occupiedRooms = bookingsSnapshot.size()
        
        // Load pending payments
        val paymentsSnapshot = firestore.collection("payments")
            .whereEqualTo("ownerId", ownerId)
            .whereEqualTo("status", "PENDING")
            .get()
            .await()
        
        val pendingPayments = paymentsSnapshot.documents.sumOf { 
            it.getDouble("amount") ?: 0.0 
        }
        
        // Load maintenance requests
        val maintenanceSnapshot = firestore.collection("maintenance_requests")
            .whereEqualTo("ownerId", ownerId)
            .whereIn("status", listOf("SUBMITTED", "IN_PROGRESS"))
            .get()
            .await()
        
        val maintenanceRequests = maintenanceSnapshot.size()
        
        // Load recent activities
        val recentActivities = loadRecentActivities(ownerId, "owner")
        
        return DashboardData(
            totalProperties = totalProperties,
            occupiedRooms = occupiedRooms,
            pendingPayments = pendingPayments,
            maintenanceRequests = maintenanceRequests,
            recentActivities = recentActivities
        )
    }

    private suspend fun loadResidentDashboardData(tenantId: String): DashboardData {
        // Load current booking
        val bookingSnapshot = firestore.collection("bookings")
            .whereEqualTo("tenantId", tenantId)
            .whereEqualTo("status", "CHECKED_IN")
            .limit(1)
            .get()
            .await()
        
        val currentBooking = bookingSnapshot.documents.firstOrNull()
        
        // Load pending payments for current booking
        val pendingPayments = if (currentBooking != null) {
            val paymentsSnapshot = firestore.collection("payments")
                .whereEqualTo("tenantId", tenantId)
                .whereEqualTo("bookingId", currentBooking.id)
                .whereEqualTo("status", "PENDING")
                .get()
                .await()
            
            paymentsSnapshot.documents.sumOf { 
                it.getDouble("amount") ?: 0.0 
            }
        } else 0.0
        
        // Load maintenance requests
        val maintenanceSnapshot = firestore.collection("maintenance_requests")
            .whereEqualTo("tenantId", tenantId)
            .get()
            .await()
        
        val maintenanceRequests = maintenanceSnapshot.size()
        
        // Load recent activities
        val recentActivities = loadRecentActivities(tenantId, "tenant")
        
        return DashboardData(
            totalProperties = 0,
            occupiedRooms = if (currentBooking != null) 1 else 0,
            pendingPayments = pendingPayments,
            maintenanceRequests = maintenanceRequests,
            recentActivities = recentActivities
        )
    }

    private suspend fun loadAdminDashboardData(): DashboardData {
        // Load total properties
        val propertiesSnapshot = firestore.collection("properties").get().await()
        val totalProperties = propertiesSnapshot.size()
        
        // Load total occupied rooms
        val bookingsSnapshot = firestore.collection("bookings")
            .whereEqualTo("status", "CHECKED_IN")
            .get()
            .await()
        val occupiedRooms = bookingsSnapshot.size()
        
        // Load total pending payments
        val paymentsSnapshot = firestore.collection("payments")
            .whereEqualTo("status", "PENDING")
            .get()
            .await()
        val pendingPayments = paymentsSnapshot.documents.sumOf { 
            it.getDouble("amount") ?: 0.0 
        }
        
        // Load total maintenance requests
        val maintenanceSnapshot = firestore.collection("maintenance_requests")
            .whereIn("status", listOf("SUBMITTED", "IN_PROGRESS"))
            .get()
            .await()
        val maintenanceRequests = maintenanceSnapshot.size()
        
        // Load recent activities
        val recentActivities = loadRecentActivities("", "admin")
        
        return DashboardData(
            totalProperties = totalProperties,
            occupiedRooms = occupiedRooms,
            pendingPayments = pendingPayments,
            maintenanceRequests = maintenanceRequests,
            recentActivities = recentActivities
        )
    }

    private suspend fun loadRecentActivities(userId: String, userType: String): List<RecentActivity> {
        // This is a simplified implementation
        // In a real app, you would load and combine different types of activities
        val activities = mutableListOf<RecentActivity>()
        
        try {
            // Load recent bookings
            val bookingsQuery = if (userType == "admin") {
                firestore.collection("bookings")
            } else if (userType == "owner") {
                firestore.collection("bookings").whereEqualTo("ownerId", userId)
            } else {
                firestore.collection("bookings").whereEqualTo("tenantId", userId)
            }
            
            val bookingsSnapshot = bookingsQuery
                .orderBy("createdAt", com.google.firebase.firestore.Query.Direction.DESCENDING)
                .limit(5)
                .get()
                .await()
            
            bookingsSnapshot.documents.forEach { doc ->
                activities.add(
                    RecentActivity(
                        id = doc.id,
                        type = "booking",
                        title = "New Booking",
                        description = "Booking status: ${doc.getString("status")}",
                        timestamp = doc.getLong("createdAt") ?: 0L
                    )
                )
            }
        } catch (e: Exception) {
            // Handle error silently for now
        }
        
        return activities.sortedByDescending { it.timestamp }.take(10)
    }
}

/**
 * Sealed class representing dashboard states
 */
sealed class DashboardState {
    object Idle : DashboardState()
    object Loading : DashboardState()
    data class Success(val data: DashboardData) : DashboardState()
    data class Error(val message: String) : DashboardState()
}

/**
 * Data class for dashboard information
 */
data class DashboardData(
    val totalProperties: Int = 0,
    val occupiedRooms: Int = 0,
    val pendingPayments: Double = 0.0,
    val maintenanceRequests: Int = 0,
    val recentActivities: List<RecentActivity> = emptyList()
)

/**
 * Data class for recent activities
 */
data class RecentActivity(
    val id: String,
    val type: String, // "booking", "payment", "maintenance", etc.
    val title: String,
    val description: String,
    val timestamp: Long
)
