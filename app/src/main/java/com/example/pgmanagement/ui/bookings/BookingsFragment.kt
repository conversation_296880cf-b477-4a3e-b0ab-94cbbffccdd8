package com.example.pgmanagement.ui.bookings

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.example.pgmanagement.databinding.FragmentBookingsBinding

/**
 * Fragment for displaying bookings
 */
class BookingsFragment : Fragment() {

    private var _binding: FragmentBookingsBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentBookingsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Setup UI
        binding.tvTitle.text = "Bookings"
        binding.tvSubtitle.text = "Manage your bookings and reservations"
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
