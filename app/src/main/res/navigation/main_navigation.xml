<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_navigation"
    app:startDestination="@id/dashboardFragment">

    <fragment
        android:id="@+id/dashboardFragment"
        android:name="com.example.pgmanagement.ui.dashboard.DashboardFragment"
        android:label="@string/dashboard"
        tools:layout="@layout/fragment_dashboard">
        <action
            android:id="@+id/action_dashboard_to_property_details"
            app:destination="@id/propertyDetailsFragment" />
        <action
            android:id="@+id/action_dashboard_to_add_property"
            app:destination="@id/addPropertyFragment" />
    </fragment>

    <fragment
        android:id="@+id/propertiesFragment"
        android:name="com.example.pgmanagement.ui.properties.PropertiesFragment"
        android:label="@string/properties"
        tools:layout="@layout/fragment_properties">
        <action
            android:id="@+id/action_properties_to_property_details"
            app:destination="@id/propertyDetailsFragment" />
        <action
            android:id="@+id/action_properties_to_add_property"
            app:destination="@id/addPropertyFragment" />
        <action
            android:id="@+id/action_properties_to_search"
            app:destination="@id/searchPropertiesFragment" />
    </fragment>

    <fragment
        android:id="@+id/bookingsFragment"
        android:name="com.example.pgmanagement.ui.bookings.BookingsFragment"
        android:label="@string/bookings"
        tools:layout="@layout/fragment_bookings">
        <action
            android:id="@+id/action_bookings_to_booking_details"
            app:destination="@id/bookingDetailsFragment" />
    </fragment>

    <fragment
        android:id="@+id/paymentsFragment"
        android:name="com.example.pgmanagement.ui.payments.PaymentsFragment"
        android:label="@string/payments"
        tools:layout="@layout/fragment_payments">
        <action
            android:id="@+id/action_payments_to_payment_details"
            app:destination="@id/paymentDetailsFragment" />
        <action
            android:id="@+id/action_payments_to_make_payment"
            app:destination="@id/makePaymentFragment" />
    </fragment>

    <fragment
        android:id="@+id/profileFragment"
        android:name="com.example.pgmanagement.ui.profile.ProfileFragment"
        android:label="@string/profile"
        tools:layout="@layout/fragment_profile">
        <action
            android:id="@+id/action_profile_to_settings"
            app:destination="@id/settingsFragment" />
        <action
            android:id="@+id/action_profile_to_maintenance"
            app:destination="@id/maintenanceFragment" />
    </fragment>

    <!-- Property Details -->
    <fragment
        android:id="@+id/propertyDetailsFragment"
        android:name="com.example.pgmanagement.ui.properties.PropertyDetailsFragment"
        android:label="Property Details"
        tools:layout="@layout/fragment_property_details">
        <argument
            android:name="propertyId"
            app:argType="string" />
        <action
            android:id="@+id/action_property_details_to_booking"
            app:destination="@id/createBookingFragment" />
        <action
            android:id="@+id/action_property_details_to_reviews"
            app:destination="@id/reviewsFragment" />
    </fragment>

    <!-- Add Property -->
    <fragment
        android:id="@+id/addPropertyFragment"
        android:name="com.example.pgmanagement.ui.properties.AddPropertyFragment"
        android:label="Add Property"
        tools:layout="@layout/fragment_add_property" />

    <!-- Search Properties -->
    <fragment
        android:id="@+id/searchPropertiesFragment"
        android:name="com.example.pgmanagement.ui.properties.SearchPropertiesFragment"
        android:label="Search Properties"
        tools:layout="@layout/fragment_search_properties" />

    <!-- Booking Details -->
    <fragment
        android:id="@+id/bookingDetailsFragment"
        android:name="com.example.pgmanagement.ui.bookings.BookingDetailsFragment"
        android:label="Booking Details"
        tools:layout="@layout/fragment_booking_details">
        <argument
            android:name="bookingId"
            app:argType="string" />
    </fragment>

    <!-- Create Booking -->
    <fragment
        android:id="@+id/createBookingFragment"
        android:name="com.example.pgmanagement.ui.bookings.CreateBookingFragment"
        android:label="Book Property"
        tools:layout="@layout/fragment_create_booking">
        <argument
            android:name="propertyId"
            app:argType="string" />
    </fragment>

    <!-- Payment Details -->
    <fragment
        android:id="@+id/paymentDetailsFragment"
        android:name="com.example.pgmanagement.ui.payments.PaymentDetailsFragment"
        android:label="Payment Details"
        tools:layout="@layout/fragment_payment_details">
        <argument
            android:name="paymentId"
            app:argType="string" />
    </fragment>

    <!-- Make Payment -->
    <fragment
        android:id="@+id/makePaymentFragment"
        android:name="com.example.pgmanagement.ui.payments.MakePaymentFragment"
        android:label="Make Payment"
        tools:layout="@layout/fragment_make_payment">
        <argument
            android:name="bookingId"
            app:argType="string" />
    </fragment>

    <!-- Settings -->
    <fragment
        android:id="@+id/settingsFragment"
        android:name="com.example.pgmanagement.ui.settings.SettingsFragment"
        android:label="Settings"
        tools:layout="@layout/fragment_settings" />

    <!-- Maintenance -->
    <fragment
        android:id="@+id/maintenanceFragment"
        android:name="com.example.pgmanagement.ui.maintenance.MaintenanceFragment"
        android:label="Maintenance Requests"
        tools:layout="@layout/fragment_maintenance">
        <action
            android:id="@+id/action_maintenance_to_create_request"
            app:destination="@id/createMaintenanceFragment" />
        <action
            android:id="@+id/action_maintenance_to_request_details"
            app:destination="@id/maintenanceDetailsFragment" />
    </fragment>

    <!-- Create Maintenance Request -->
    <fragment
        android:id="@+id/createMaintenanceFragment"
        android:name="com.example.pgmanagement.ui.maintenance.CreateMaintenanceFragment"
        android:label="Submit Request"
        tools:layout="@layout/fragment_create_maintenance" />

    <!-- Maintenance Details -->
    <fragment
        android:id="@+id/maintenanceDetailsFragment"
        android:name="com.example.pgmanagement.ui.maintenance.MaintenanceDetailsFragment"
        android:label="Request Details"
        tools:layout="@layout/fragment_maintenance_details">
        <argument
            android:name="maintenanceId"
            app:argType="string" />
    </fragment>

    <!-- Reviews -->
    <fragment
        android:id="@+id/reviewsFragment"
        android:name="com.example.pgmanagement.ui.reviews.ReviewsFragment"
        android:label="Reviews"
        tools:layout="@layout/fragment_reviews">
        <argument
            android:name="propertyId"
            app:argType="string" />
        <action
            android:id="@+id/action_reviews_to_write_review"
            app:destination="@id/writeReviewFragment" />
    </fragment>

    <!-- Write Review -->
    <fragment
        android:id="@+id/writeReviewFragment"
        android:name="com.example.pgmanagement.ui.reviews.WriteReviewFragment"
        android:label="Write Review"
        tools:layout="@layout/fragment_write_review">
        <argument
            android:name="propertyId"
            app:argType="string" />
    </fragment>

</navigation>
