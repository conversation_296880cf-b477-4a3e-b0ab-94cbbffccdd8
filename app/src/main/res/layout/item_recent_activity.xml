<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/Widget.PGManagement.CardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:clickable="true"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Activity Icon -->
        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/circle_background"
            android:padding="8dp"
            android:src="@drawable/ic_dashboard"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/primary_blue" />

        <!-- Activity Title -->
        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:fontFamily="sans-serif-medium"
            android:textColor="@color/text_primary_light"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/tvTimestamp"
            app:layout_constraintStart_toEndOf="@+id/ivIcon"
            app:layout_constraintTop_toTopOf="@+id/ivIcon"
            tools:text="New Booking Received" />

        <!-- Activity Description -->
        <TextView
            android:id="@+id/tvDescription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:textColor="@color/text_secondary_light"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@+id/tvTimestamp"
            app:layout_constraintStart_toEndOf="@+id/ivIcon"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle"
            tools:text="John Doe booked Room 101 for 6 months" />

        <!-- Timestamp -->
        <TextView
            android:id="@+id/tvTimestamp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_hint_light"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvTitle"
            tools:text="2 hours ago" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
