<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.profile.ProfileFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Profile Header -->
        <com.google.android.material.card.MaterialCardView
            style="@style/Widget.PGManagement.CardView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- Profile Image -->
                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/ivProfileImage"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_marginBottom="16dp"
                    android:src="@drawable/ic_person"
                    app:civ_border_color="@color/primary_blue"
                    app:civ_border_width="2dp" />

                <!-- User Name -->
                <TextView
                    android:id="@+id/tvUserName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    android:fontFamily="sans-serif-medium"
                    android:text="User Name"
                    android:textColor="@color/text_primary_light"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <!-- User Type -->
                <TextView
                    android:id="@+id/tvUserType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/user_type_badge"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="4dp"
                    android:text="PG Owner"
                    android:textColor="@color/primary_blue"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Profile Options -->
        <com.google.android.material.card.MaterialCardView
            style="@style/Widget.PGManagement.CardView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <!-- Edit Profile -->
                <LinearLayout
                    android:id="@+id/layoutEditProfile"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/ic_edit"
                        app:tint="@color/text_secondary_light" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Edit Profile"
                        android:textColor="@color/text_primary_light"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/text_secondary_light" />

                </LinearLayout>

                <!-- Settings -->
                <LinearLayout
                    android:id="@+id/layoutSettings"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/ic_settings"
                        app:tint="@color/text_secondary_light" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Settings"
                        android:textColor="@color/text_primary_light"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/text_secondary_light" />

                </LinearLayout>

                <!-- Help & Support -->
                <LinearLayout
                    android:id="@+id/layoutHelp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/ic_help"
                        app:tint="@color/text_secondary_light" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Help &amp; Support"
                        android:textColor="@color/text_primary_light"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/text_secondary_light" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Logout Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnLogout"
            style="@style/Widget.PGManagement.Button.Outlined"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="@string/logout"
            android:textColor="@color/error_red"
            app:strokeColor="@color/error_red" />

        <!-- App Version -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:text="Version 1.0.0"
            android:textColor="@color/text_hint_light"
            android:textSize="12sp" />

    </LinearLayout>

</ScrollView>
