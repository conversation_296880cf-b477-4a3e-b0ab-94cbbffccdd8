<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".ui.bookings.BookingsFragment">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:fontFamily="sans-serif-medium"
        android:text="Bookings"
        android:textColor="@color/text_primary_light"
        android:textSize="24sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvSubtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:text="Manage your bookings"
        android:textColor="@color/text_secondary_light"
        android:textSize="14sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="Bookings feature coming soon..."
        android:textColor="@color/text_secondary_light"
        android:textSize="16sp" />

</LinearLayout>
