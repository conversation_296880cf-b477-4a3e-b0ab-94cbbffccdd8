<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.properties.PropertiesFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header Section -->
            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:fontFamily="sans-serif-medium"
                android:text="Properties"
                android:textColor="@color/text_primary_light"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvSubtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:text="Manage your properties"
                android:textColor="@color/text_secondary_light"
                android:textSize="14sp" />

            <!-- Search Section -->
            <com.google.android.material.card.MaterialCardView
                style="@style/Widget.PGManagement.CardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:hint="Search properties..."
                        app:boxCornerRadiusBottomEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusTopStart="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etSearch"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnSearch"
                        style="@style/Widget.Material3.Button.Icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        app:icon="@drawable/ic_search" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Filter Chips -->
            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chipGroup"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:singleSelection="false">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipAll"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="All" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipAvailable"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Available" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipOccupied"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Occupied" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipMaintenance"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Maintenance" />

            </com.google.android.material.chip.ChipGroup>

            <!-- Properties List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvProperties"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:itemCount="5"
                tools:listitem="@layout/item_property" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/layoutEmptyState"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="48dp"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    android:alpha="0.5"
                    android:src="@drawable/ic_properties"
                    app:tint="@color/gray_400" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:fontFamily="sans-serif-medium"
                    android:text="No Properties Found"
                    android:textColor="@color/text_secondary_light"
                    android:textSize="18sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="Start by adding your first property or adjust your search filters"
                    android:textColor="@color/text_hint_light"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddProperty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/add_property"
        android:src="@drawable/ic_add"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
