<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/Widget.PGManagement.CardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    android:clickable="true"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Property Image -->
        <ImageView
            android:id="@+id/ivPropertyImage"
            android:layout_width="0dp"
            android:layout_height="200dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_properties"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Status Badge -->
        <TextView
            android:id="@+id/tvStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:background="@drawable/status_badge_background"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:text="Available"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@+id/ivPropertyImage"
            app:layout_constraintTop_toTopOf="@+id/ivPropertyImage"
            tools:text="Available" />

        <!-- Content Container -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivPropertyImage">

            <!-- Property Name -->
            <TextView
                android:id="@+id/tvPropertyName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:fontFamily="sans-serif-medium"
                android:textColor="@color/text_primary_light"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="Sunshine PG for Boys" />

            <!-- Property Address -->
            <TextView
                android:id="@+id/tvPropertyAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:drawablePadding="4dp"
                android:textColor="@color/text_secondary_light"
                android:textSize="14sp"
                app:drawableStartCompat="@drawable/ic_location"
                app:drawableTint="@color/text_secondary_light"
                tools:text="Sector 15, Noida, UP" />

            <!-- Property Details Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:orientation="horizontal">

                <!-- Rooms Info -->
                <TextView
                    android:id="@+id/tvRoomsInfo"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawablePadding="4dp"
                    android:textColor="@color/text_secondary_light"
                    android:textSize="12sp"
                    app:drawableStartCompat="@drawable/ic_bed"
                    app:drawableTint="@color/text_secondary_light"
                    tools:text="5/10 Rooms" />

                <!-- Rating -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginEnd="2dp"
                        android:src="@drawable/ic_star"
                        app:tint="@color/warning_yellow" />

                    <TextView
                        android:id="@+id/tvRating"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_secondary_light"
                        android:textSize="12sp"
                        tools:text="4.5 (25)" />

                </LinearLayout>

            </LinearLayout>

            <!-- Price and Action Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <!-- Price -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvPrice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif-medium"
                        android:textColor="@color/primary_blue"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        tools:text="₹12,000" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:text="/month"
                        android:textColor="@color/text_secondary_light"
                        android:textSize="12sp" />

                </LinearLayout>

                <!-- Action Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnAction"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="View Details"
                    android:textSize="12sp"
                    app:cornerRadius="8dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
