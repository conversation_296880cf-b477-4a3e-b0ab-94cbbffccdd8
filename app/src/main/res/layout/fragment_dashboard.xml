<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.dashboard.DashboardFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Welcome Section -->
            <TextView
                android:id="@+id/tvWelcome"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:fontFamily="sans-serif-medium"
                android:text="@string/welcome_back"
                android:textColor="@color/text_primary_light"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:textColor="@color/text_secondary_light"
                android:textSize="14sp"
                tools:text="Today, March 15, 2024" />

            <!-- Statistics Cards Grid -->
            <GridLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:columnCount="2"
                android:rowCount="3">

                <!-- Total Properties Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardProperties"
                    style="@style/Widget.PGManagement.CardView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardBackgroundColor="@color/primary_blue_light">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:id="@+id/tvTotalProperties"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/primary_blue_dark"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            tools:text="5" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/total_properties"
                            android:textColor="@color/primary_blue_dark"
                            android:textSize="12sp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Occupied Rooms Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardTenants"
                    style="@style/Widget.PGManagement.CardView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardBackgroundColor="@color/success_green_light">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:id="@+id/tvOccupiedRooms"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/success_green_dark"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            tools:text="12" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/occupied_rooms"
                            android:textColor="@color/success_green_dark"
                            android:textSize="12sp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Pending Payments Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardPayments"
                    style="@style/Widget.PGManagement.CardView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardBackgroundColor="@color/warning_yellow_light">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:id="@+id/tvPendingPayments"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/warning_yellow_dark"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            tools:text="₹25,000" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/pending_payments"
                            android:textColor="@color/warning_yellow_dark"
                            android:textSize="12sp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Maintenance Requests Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardMaintenance"
                    style="@style/Widget.PGManagement.CardView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardBackgroundColor="@color/error_red_light">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:id="@+id/tvMaintenanceRequests"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/error_red_dark"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            tools:text="3" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/maintenance_requests"
                            android:textColor="@color/error_red_dark"
                            android:textSize="12sp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Resident-specific cards (hidden by default) -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardRentDue"
                    style="@style/Widget.PGManagement.CardView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:visibility="gone"
                    app:cardBackgroundColor="@color/accent_orange_light">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:id="@+id/tvRentDue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/accent_orange_dark"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            tools:text="₹15,000" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Rent Due"
                            android:textColor="@color/accent_orange_dark"
                            android:textSize="12sp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardMyBookings"
                    style="@style/Widget.PGManagement.CardView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:visibility="gone"
                    app:cardBackgroundColor="@color/primary_blue_light">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:id="@+id/tvMyBookings"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/primary_blue_dark"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            tools:text="1" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="My Bookings"
                            android:textColor="@color/primary_blue_dark"
                            android:textSize="12sp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardRevenue"
                    style="@style/Widget.PGManagement.CardView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:visibility="gone"
                    app:cardBackgroundColor="@color/success_green_light">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:id="@+id/tvRevenue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/success_green_dark"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            tools:text="₹1,50,000" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Monthly Revenue"
                            android:textColor="@color/success_green_dark"
                            android:textSize="12sp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </GridLayout>

            <!-- Recent Activities Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:fontFamily="sans-serif-medium"
                android:text="@string/recent_activities"
                android:textColor="@color/text_primary_light"
                android:textSize="18sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvRecentActivities"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:itemCount="3"
                tools:listitem="@layout/item_recent_activity" />

            <TextView
                android:id="@+id/tvNoActivities"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:gravity="center"
                android:text="@string/no_data"
                android:textColor="@color/text_secondary_light"
                android:textSize="16sp"
                android:visibility="gone" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddProperty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/add_property"
        android:src="@drawable/ic_add"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
